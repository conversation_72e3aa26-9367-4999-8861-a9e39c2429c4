variable "iam_config" {
  description = "IAM configuration loaded from YAML"
  type = object({
    tags                     = map(string)
    service_roles            = map(object({ assume_role_policy = string }))
    custom_policies          = map(object({ description = string, policy = string }))
    role_policy_attachments  = map(object({ role = string, policy_arn = string }))
    users                    = list(string)
    groups                   = list(string)
    group_membership         = map(object({ group = string, users = list(string) }))
    group_policy_attachments = map(object({ group = string, policy_arn = string }))
  })
}
