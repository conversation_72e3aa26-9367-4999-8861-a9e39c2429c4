#  EC2 Module Variables - Object Type

variable "ec2_config" {
  description = "EC2 configuration object"
  type = object({
    instance_type = string
    volume_size   = number
    instance_name = string
    key_name      = string
    region        = string
    subnet_id     = string
    ami_id        = string
  })
}

variable "security_group_id" {
  description = "Security Group ID from sg-module output"
  type        = string
}

variable "security_group_name" {
  description = "Security Group Name from sg-module output (optional)"
  type        = string
  default     = ""
}

# variable "common_tags" {
#   description = "Common tags to apply to all resources"
#   type        = map(string)
#   default = {
#     ManagedBy = "terraform"
#   }
# }