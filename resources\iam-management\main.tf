locals {
  # Load base YAML configs
  env = merge(
    yamldecode(file("${path.module}/../../environments/region.yaml")),
    yamldecode(file("${path.module}/../../environments/webforx.yaml"))
  )

  # Load external user/group/membership lists
  users_input      = yamldecode(file("${path.module}/../../environments/users.yaml"))
  groups_input     = yamldecode(file("${path.module}/../../environments/groups.yaml"))
  membership_input = yamldecode(file("${path.module}/../../environments/group_membership.yaml"))

  # Final config passed to the module
  config = {
    tags                     = local.env.tags
    service_roles            = local.env.iam_config.service_roles
    custom_policies          = local.env.iam_config.custom_policies
    role_policy_attachments  = local.env.iam_config.role_policy_attachments
    users                    = local.users_input.users
    groups                   = local.groups_input.groups
    group_membership         = local.membership_input.group_membership
    group_policy_attachments = local.env.iam_config.group_policy_attachments
  }
}

terraform {
  required_version = ">= 1.10.5"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    bucket         = "development-webforx-sandbox-tf-state"
    key            = "connect/iam/bastion/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "development-webforx-sandbox-tf-state-lock"
  }
}

provider "aws" {
  region = local.env.dev
}

module "iam-management" {
  source     = "../../modules/iam-management"
  iam_config = local.config
}
