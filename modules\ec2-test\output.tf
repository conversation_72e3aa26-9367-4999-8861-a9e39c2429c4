output "instance_id" {
  value = aws_instance.ec2_instance.id
}
output "instance_public_ip" {
  value = aws_instance.ec2_instance.public_ip
}
output "instance_private_ip" {
  value = aws_instance.ec2_instance.private_ip
}
output "instance_public_dns" {
  value = aws_instance.ec2_instance.public_dns
}
output "instance_arn" {
  value = aws_instance.ec2_instance.arn
}
output "instance_state" {
  value = aws_instance.ec2_instance.instance_state
}
output "availability_zone" {
  value = aws_instance.ec2_instance.availability_zone
}
output "key_name" {
  value = aws_instance.ec2_instance.key_name
}
output "security_groups" {
  value = aws_instance.ec2_instance.vpc_security_group_ids
}
output "instance_tags" {
  value = aws_instance.ec2_instance.tags
}