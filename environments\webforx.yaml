tags:
  owner: "Webforx Technology"
  team: "Webforx Team"
  environment: "development"
  project: "webforx"
  create_by: "Terraform"
  cloud_provider: "aws"
  confidentiality: "internal"
  costcenter: "dev-webforx-eng"

  aws_account: "sandbox"

  module: "rds"

terraform_backend:
  s3_bucket: "development-webforx-sandbox-tf-state"
  s3_key: "webforx/rds/sandbox/terraform.tfstate"
  dynamodb_table: "development-webforx-sandbox-tf-state-lock"
  use_lockfile: true     


  module: "rds"


global:
  environment: "main"

s3:
  aws_region_main: "us-east-1"
  aws_region_backup: "us-east-2"
  force_destroy: false

auto-scaling-group:
  aws_region_main: "us-east-1"
  name: "default-asg-demo"
  ami_id: "ami-00a929b66ed6e0de6"
  instance_type: "t3.micro"
  desired_capacity: 2
  min_size: 1
  max_size: 4
  cpu_target: 60


auto-scaling-group:
  aws_region_main: "us-east-1"
  name: "default-asg-demo"
  ami_id: "ami-00a929b66ed6e0de6"
  instance_type: "t3.micro"
  aws_region_main: "us-east-1"
  name: "default-asg-demo"
  ami_id: "ami-00a929b66ed6e0de6"
  instance_type: "t3.micro"
  desired_capacity: 2
  min_size: 1
  max_size: 4
  cpu_target: 60
  min_size: 1
  max_size: 4
  cpu_target: 60


kms:
  ebs_s3_ssm_autoscaling:
    alias: "webforx/sandbox"
    description: "KMS key for EBS"
    deletion_window: 7


lambda_kms_compliant_check:
  lambda_function_name: "kms-compliant-function"
  dynamodb_table_name: "kms-compliant-logs"
  role_name: "kms-compliant-lambda-role"
  mattermost_webhook_url: "https://mattermost.edusc.us/hooks/c9fiz1xjei8mpeiijypwun7iyc" #used for test only. have deleted the hook
  lambda_run_schedule: "cron(0 0 L * ? *)"
  log_retention_days: 30
  runtime: "python3.10"
  timeout: 900


lambda_kms_compliant_check:
  lambda_function_name: "kms-compliant-function"
  dynamodb_table_name: "kms-compliant-logs"
  role_name: "kms-compliant-lambda-role"
  mattermost_webhook_url: "https://mattermost.edusc.us/hooks/c9fiz1xjei8mpeiijypwun7iyc" #used for test only. have deleted the hook
  lambda_run_schedule: "cron(0 0 L * ? *)"
  log_retention_days: 30
  runtime: "python3.10"
  timeout: 900

aws-budget:
  budget_limit: 200

  thresholds: [ 80, 100 ]
  email_subscribers:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>

ec2:
  aws_region: "us-east-1"
  ec2_instance_ami: "ami-0eebd8a9879bdfafc"
  create_on_public_subnet: true
  ec2_instance_type: "t2.micro"
  root_volume_size: "20"
  instance_name: "backend"
  ec2_instance_key_name: "webforx-engineering"
  enable_termination_protection: false
  sg_name: "backend"
  allowed_ports: [ 22, 80, 443 ]
  allowed_ips:
    vpn: "*************/32"
  vpc_id: "vpc-0cd5e30f5b5cc9770"
  private_subnet: "subnet-05825d1a5df058e41"
  public_subnet: "subnet-0c6820f5859c2f10f"
  iam_role_name: "ec2_role"
  iam_policy_arn: "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess"
  iam_instance_profile_name: "ec2_policy"

vpc-core-network:
  vpc_cidr: ********/16
  enable_dns_support: true
  enable_dns_hostnames: true
  availability_zones: [ "us-east-1a", "us-east-1b", "us-east-1c" ]
  control_plane_name: " "

  open_inbound_acl_rules:
  - rule_number: 100
    rule_action: "allow"
    protocol: "-1"
    cidr_block: "0.0.0.0/0"

  open_outbound_acl_rules:
  - rule_number: 100
    rule_action: "allow"
    protocol: "-1"
    cidr_block: "0.0.0.0/0"

delete_default_vpc: false

aws_backup:
  vault_name: "sandbox-backup-vault"   
  plan_name: "sandbox-ec2-backup-plan"
  rule_name: "sandbox-daily"
  schedule: "cron(0 4 * * ? *)"
  delete_after_days: 14
  selection_name: "sandbox-ec2-selection"
  selection_tag_key: "Backup"
  selection_tag_value: "true"
  iam_role_arn: "arn:aws:iam::298350610518:role/AWSBackupDefaultServiceRole"
kms_config:
  arn: "arn:aws:kms:us-east-1:298350610518:key/ef7440d6-690b-4f9d-9bbb-68668b391793"

ami_cleanup:
  lambda_name: "ami-cleanup-lambda"
  ami_tag_key: "Backup"
  ami_tag_value_prefix: "true"
  sns_topic_arn: "arn:aws:sns:us-east-1:298350610518:ami-cleanup-notifications"   
  cleanup_schedule: "cron(0 4 * * ? *)"
  mattermost_webhook_url: "https://mattermost.edusc.us/hooks/ptpdeuou6tf5ppczdw3cijj5be"
  
  notification_emails:


aws-budget:
  budget_limit: 200

  thresholds: [80, 100]
  email_subscribers:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

    - <EMAIL>
    - <EMAIL>

    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>

vpc:
  vpc_id: "vpc-0cbbdd3c1aec0ffd2"
  route_table_id: "rtb-0e956e3392118eb85"


kms:
  ebs_s3_ssm_autoscaling:
    alias: "webforx/sandbox"
    description: "KMS key for EBS"
    deletion_window: 7

lambda_kms_compliant_check:
  lambda_function_name: "kms-compliant-function"
  dynamodb_table_name: "kms-compliant-logs"
  role_name: "kms-compliant-lambda-role"
  mattermost_webhook_url: "https://mattermost.edusc.us/hooks/c9fiz1xjei8mpeiijypwun7iyc" #used for test only. have deleted the hook
  lambda_run_schedule: "cron(0/5 * ? * * *)" #lambda_run_schedule: "cron(0 0 L * ? *)"
  log_retention_days: 30
  runtime: "python3.10"
  timeout: 900


rds:
  kms_config:
    description: "KMS key for shared Webforx RDS infrastructure"
    deletion_window_in_days: 30
    enable_key_rotation: true
    rotation_alias_name: "alias/webforx-rds-key"

  config:
    aws_region_main: "us-east-1"
    name: "webforx-core"
    engine: "postgres"
    engine_version: "14.18"
    parameter_group_family: "postgres14"
    instance_class: "db.t3.micro"
    allocated_storage: 10
    max_allocated_storage: 25
    port: 5432
    multi_az: true
    publicly_accessible: false
    vpc_id: "vpc-09e26d6ae47f2a3c6"
    private_subnet_ids:
      - "subnet-0090f0a4bfa78c620"
      - "subnet-0965ec4f4e9057e0b"
    app_security_group_id: "sg-0a0278f410548145f"
    subnet_group_name: "rds-subnet-group"
    monitoring_interval: 60
    monitoring_role_name: "rds-monitor"
    performance_retention: 7
    skip_final_snapshot: true
    backup_retention_days: 7
    sns_topic_arn: "arn:aws:sns:us-east-1:298350610518:webforx-rds-alerts"  
    sns_topic_arn_dev: "arn:aws:sns:us-east-1:298350610518:sandbox-development-alerts"
    mattermost_webhook_url: "https://mattermost.edusc.us/hooks/ptpdeuou6tf5ppczdw3cijj5be"
    sns_topic_name: "webforx-rds-alerts"
    performance_insights_enabled: true
    ssm_username_param: "/rds/dev/username"
    ssm_password_param: "/rds/dev/password"
    ssm_dbname_param: "/rds/dev/dbname"
    notification_emails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    
  monitoring_config:
    namespace: "AWS/RDS"
    cpu_metric_name: "CPUUtilization"
    cpu_period: 300
    cpu_statistic: "Average"
    cpu_threshold: 85
    cpu_evaluation_periods: 2
    cpu_comparison_operator: "GreaterThanThreshold"
    cpu_alarm_description: "Webforx RDS: High CPU usage"

    storage_metric_name: "FreeStorageSpace"
    storage_period: 600
    storage_statistic: "Minimum"
    storage_threshold: 10737418240
    storage_evaluation_periods: 1
    storage_comparison_operator: "LessThanThreshold"
    storage_alarm_description: "Webforx RDS: Low storage warning"


notification_emails:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>



budget_limit: 200
thresholds: [ 80, 100 ]
email_subscribers:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>

rds:
  kms_config:
    description: "KMS key for shared Webforx RDS infrastructure"
    deletion_window_in_days: 30
    enable_key_rotation: true
    rotation_alias_name: "alias/webforx-rds-key"

  config:
    aws_region_main: "us-east-1"
    name: "webforx-core"
    engine: "postgres"
    engine_version: "14.18"
    parameter_group_family: "postgres14"
    instance_class: "db.t3.micro"
    allocated_storage: 10
    max_allocated_storage: 25
    port: 5432
    multi_az: true
    publicly_accessible: false
    vpc_id: "vpc-09e26d6ae47f2a3c6"
    private_subnet_ids:
      - "subnet-0090f0a4bfa78c620"
      - "subnet-0965ec4f4e9057e0b"
    app_security_group_id: "sg-0a0278f410548145f"
    subnet_group_name: "rds-subnet-group"
    monitoring_interval: 60
    monitoring_role_name: "rds-monitor"
    performance_retention: 7
    skip_final_snapshot: true
    backup_retention_days: 7
    sns_topic_arn: "arn:aws:sns:us-east-1:298350610518:webforx-rds-alerts"  
    sns_topic_arn_dev: "arn:aws:sns:us-east-1:298350610518:sandbox-development-alerts"
    mattermost_webhook_url: "https://mattermost.edusc.us/hooks/ptpdeuou6tf5ppczdw3cijj5be"
    sns_topic_name: "webforx-rds-alerts"
    performance_insights_enabled: true
    ssm_username_param: "/rds/dev/username"
    ssm_password_param: "/rds/dev/password"
    ssm_dbname_param: "/rds/dev/dbname"
    notification_emails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    
  monitoring_config:
    namespace: "AWS/RDS"
    cpu_metric_name: "CPUUtilization"
    cpu_period: 300
    cpu_statistic: "Average"
    cpu_threshold: 85
    cpu_evaluation_periods: 2
    cpu_comparison_operator: "GreaterThanThreshold"
    cpu_alarm_description: "Webforx RDS: High CPU usage"

    storage_metric_name: "FreeStorageSpace"
    storage_period: 600
    storage_statistic: "Minimum"
    storage_threshold: 10737418240
    storage_evaluation_periods: 1
    storage_comparison_operator: "LessThanThreshold"
    storage_alarm_description: "Webforx RDS: Low storage warning"



  
ec2:
  aws_region: "us-east-1"
  ec2_instance_ami: "ami-0eebd8a9879bdfafc"
  create_on_public_subnet: true
  ec2_instance_type: "t2.micro"
  root_volume_size: "20"
  instance_name: "backend"
  ec2_instance_key_name: "webforx-engineering"
  enable_termination_protection: false
  sg_name: "backend"
  allowed_ports: [ 22, 80, 443 ]
  allowed_ips:
    vpn: "*************/32"
  vpc_id: "vpc-0cd5e30f5b5cc9770"
  private_subnet: "subnet-05825d1a5df058e41"
  public_subnet: "subnet-0c6820f5859c2f10f"
  iam_role_name: "ec2_role"
  iam_policy_arn: "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess"
  iam_instance_profile_name: "ec2_policy"

vpc-core-network:
  vpc_cidr: ********/16
  enable_dns_support: true
  enable_dns_hostnames: true
  availability_zones: [ "us-east-1a", "us-east-1b", "us-east-1c" ]
  control_plane_name: " "

  open_inbound_acl_rules:
  - rule_number: 100
    rule_action: "allow"
    protocol: "-1"
    cidr_block: "0.0.0.0/0"

  open_outbound_acl_rules:
  - rule_number: 100
    rule_action: "allow"
    protocol: "-1"
    cidr_block: "0.0.0.0/0"

delete_default_vpc: false

aws_backup:
  vault_name: "sandbox-backup-vault"   
  plan_name: "sandbox-ec2-backup-plan"
  rule_name: "sandbox-daily"
  schedule: "cron(0 4 * * ? *)"
  delete_after_days: 14
  selection_name: "sandbox-ec2-selection"
  selection_tag_key: "Backup"
  selection_tag_value: "true"
  iam_role_arn: "arn:aws:iam::298350610518:role/AWSBackupDefaultServiceRole"
kms_config:
  arn: "arn:aws:kms:us-east-1:298350610518:key/ef7440d6-690b-4f9d-9bbb-68668b391793"

ami_cleanup:
  lambda_name: "ami-cleanup-lambda"
  ami_tag_key: "Backup"
  ami_tag_value_prefix: "true"
  sns_topic_arn: "arn:aws:sns:us-east-1:298350610518:ami-cleanup-notifications"   
  cleanup_schedule: "cron(0 4 * * ? *)"
  mattermost_webhook_url: "https://mattermost.edusc.us/hooks/ptpdeuou6tf5ppczdw3cijj5be"
  
  notification_emails:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>


vpc:
  vpc_id: "vpc-0cbbdd3c1aec0ffd2"
  route_table_id: "rtb-0e956e3392118eb85"

lambda_function:
  aws_region_main: "us-east-1"
  lambda_timeout_ec2: 300
  lambda_timeout_logger: 120
  cost_threshold: 10
  micro_max_age_days: 4
  default_max_age_days: 1
  mattermost_webhook_url: https://mattermost.edusc.us/hooks/toehkp7b77fa3gn3czbnnu55xo

eks:
  eks_version: "1.32"
  vpc_id: "vpc-09e26d6ae47f2a3c6"
  public_subnet_ids:
    - "subnet-03c3e8d9e5f31ffac"
    - "subnet-06343fecd2aec18df"
  private_subnet_ids:
    - "subnet-0090f0a4bfa78c620"
    - "subnet-0b6ce84daa47be0f9"
  endpoint_private_access: false
  endpoint_public_access: true
  ec2_ssh_key: ""
  capacity_type: "ON_DEMAND"
  ami_type: "AL2_x86_64"
  instance_types:
    - "t2.medium"
  disk_size: 10
  green: true
  blue: false
  green_node_color: "green"
  blue_node_color: "blue"
  shared_owned: "shared"
  enable_cluster_autoscaler: true
  control_plane_name: "development-webforx"
  node_min: 1
  desired_node: 1
  node_max: 5

iam_config:
  service_roles:
    ec2_role:
      assume_role_policy: roles/ec2-trust-policy.json
    eks_cluster_role:
      assume_role_policy: roles/eks-cluster-trust-policy.json
    eks_nodegroup_role:
      assume_role_policy: roles/eks-nodegroup-trust-policy.json

  custom_policies:
    s3_read_only:
      description: Read-only access to S3
      policy: policies/s3-read-only.json
    eks_cluster_policy:
      description: EKS cluster limited access
      policy: policies/eks-cluster-policy.json
    eks_nodegroup_policy:
      description: EKS node group logging and EC2 discovery
      policy: policies/eks-nodegroup-policy.json

  role_policy_attachments:
    ec2_attach_s3:
      role: ec2_role
      policy_arn: arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess
    eks_cluster_attach:
      role: eks_cluster_role
      policy_arn: arn:aws:iam::aws:policy/AmazonEKSClusterPolicy
    eks_nodegroup_attach:
      role: eks_nodegroup_role
      policy_arn: arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy

  group_policy_attachments:
    dev_group_admin:
      group: dev
      policy_arn: arn:aws:iam::aws:policy/AdministratorAccess
    qa_group_readonly:
      group: qa
      policy_arn: arn:aws:iam::aws:policy/ReadOnlyAccess
    security_group_audit:
      group: security
      policy_arn: arn:aws:iam::aws:policy/SecurityAudit



security-group:
  vpc_id: "vpc-04e141361d59ba12f"
  sg_name: "sg"
  allowed_ips: 
    allow_all_traffic_from_vpn: "*************/32"
  allowed_ports:
    - 22
    - 443


ec2_config:
  instance_type: "t2.micro"
  volume_size: 10
  instance_name: "ec2-test"
  key_name: "terraform_key"
  region: "us-east-1"
  subnet_id: "subnet-080ae30c3b2f0782f"  # Replace with your actual public subnet ID
  ami_id: "ami-020cba7c55df1f615"  # Ubuntu 22.04 LTS in us-east-1 (replace if needed)


  