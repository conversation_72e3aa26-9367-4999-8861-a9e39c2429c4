terraform {
  required_version = ">= 1.10.5"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}
# EC2 Instance Resource
resource "aws_instance" "ec2_instance" {
  ami                         = var.ec2_config.ami_id
  instance_type               = var.ec2_config.instance_type
  key_name                    = var.ec2_config.key_name
  subnet_id                   = var.ec2_config.subnet_id
  vpc_security_group_ids      = [var.security_group_id]
  associate_public_ip_address = true

  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.ec2_config.volume_size
    delete_on_termination = true
    encrypted             = true
  }

#   tags = merge(
#     var.common_tags,
#     {
#       Name              = var.ec2_config.instance_name
#       InstanceType      = var.ec2_config.instance_type
#       SecurityGroup     = var.security_group_name != "" ? var.security_group_name : "unknown"
#       OperatingSystem   = "Ubuntu 22.04 LTS"
#     }
#   )

  # # User data script for initial setup (optional)
  # user_data = base64encode(<<-EOF
  #             #!/bin/bash
  #             apt-get update
  #             apt-get install -y nginx
  #             systemctl start nginx
  #             systemctl enable nginx
  #             echo "<h1>Welcome to ${var.ec2_config.instance_name}</h1>" > /var/www/html/index.html
  #             echo "<p>Instance ID: $(curl -s http://***************/latest/meta-data/instance-id)</p>" >> /var/www/html/index.html
  #             echo "<p>Security Group: ${var.security_group_name}</p>" >> /var/www/html/index.html
  #             EOF
  # )

  lifecycle {
    create_before_destroy = true
  }
}