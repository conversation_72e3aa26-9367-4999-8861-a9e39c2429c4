IAM Management with Terraform

This project manages AWS IAM (Identity and Access Management) resources using Terraform, structured

Terraform IAM Management for Webforx

This project provisions and manages AWS IAM resources — including roles, users, groups, policies, and memberships — using a modular, scalable, and maintainable Terraform setup with YAML configuration.

---

Project Structure

project-root/
├── environment/
│   ├── webforx.yaml              # IAM roles, policies, attachments
│   ├── users.yaml                # List of IAM users
│   ├── groups.yaml               # List of IAM groups
│   └── group_membership.yaml     # Mapping of users to groups
│
├── module/
│   └── iam-management/
│       ├── iam.tf                # IAM resource definitions
│       ├── variables.tf          # Module input variables
│       ├── outputs.tf            # Module outputs
│       ├── roles/                # JSON trust policies (e.g., ec2, eks)
│       │   ├── ec2-trust-policy.json
│       │   └── eks-cluster-trust-policy.json
│       └── policies/             # JSON custom IAM policies
│           ├── s3-read-only.json
│           └── eks-cluster-policy.json
│
├── resource/
│   └── iam-management/
│       └── main.tf               # Loads YAML and applies the IAM module


 Terraform IAM Management for Webforx

This project automates the provisioning of AWS IAM resources such as **users**, **groups**, **roles**, and **policies** using Terraform. All configurations are externalized in YAML, making the solution modular, scalable, and easy to maintain across environments.

What This Deploys

Using the configuration in `webforx.yaml` and associated YAML files, this Terraform deployment will:

Create multiple IAM roles with custom **trust policies**
Attach managed AWS policies or **custom inline policies** to those roles
Create IAM **users** and **IAM groups**
Automatically assign users to groups from `group_membership.yaml`
Attach policies to groups
Keep all configuration **DRY** and externalized via YAML


Key Features & Advantages

Modular Design
All reusable IAM logic is encapsulated inside `module/iam-management`, allowing you to:

Reuse the same module across **multiple environments**
Maintain environment-specific configs in `environment/`

Configuration as YAML
Clean separation between **logic** (Terraform) and **configuration** (YAML)
Enables **non-technical users** to modify users/groups/roles without editing `.tf` files

Organized Policy Files
Trust policies stored in `roles/`
Inline policies stored in `policies/`
JSON paths are referenced **dynamically** from YAML, keeping Terraform clean

Explicit Dependencies
Group creation depends on group membership
Group membership depends on user creation
This avoids runtime errors like `NoSuchEntity` or `DeleteConflict`