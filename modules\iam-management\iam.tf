# Create IAM roles with trust policies loaded from external JSON files
resource "aws_iam_role" "service_roles" {
  for_each = var.iam_config.service_roles
  name = each.key
  # Load the assume role policy JSON file specified in the YAML config
  assume_role_policy = file("${path.module}/${each.value.assume_role_policy}")
  tags = var.iam_config.tags
}

# Create custom IAM policies from external JSON documents
resource "aws_iam_policy" "custom_policies" {
  for_each = var.iam_config.custom_policies
  name        = each.key
  description = each.value.description
  # Load the policy JSON content from the file path in YAML
  policy = file("${path.module}/${each.value.policy}")
}

# Attach AWS-managed or custom policies to IAM roles
resource "aws_iam_role_policy_attachment" "attach_custom_policies" {
  for_each = var.iam_config.role_policy_attachments
  role       = aws_iam_role.service_roles[each.value.role].name
  policy_arn = each.value.policy_arn
}

# Create IAM users defined in the YAML configuration
resource "aws_iam_user" "users" {
  for_each = toset(var.iam_config.users)
  name = each.value
  tags = var.iam_config.tags
  
}

# Create IAM groups defined in the YAML configuration
resource "aws_iam_group" "groups" {
  for_each = toset(var.iam_config.groups)
  name = each.value

}

# Define IAM group membership (which users belong to which groups)
resource "aws_iam_group_membership" "group_membership" {
  for_each = var.iam_config.group_membership
  name  = "${each.key}_membership"
  users = each.value.users
  group = aws_iam_group.groups[each.value.group].name
  depends_on = [
    aws_iam_user.users,
    aws_iam_group.groups
  ]

}

# Attach AWS-managed policies to IAM groups
resource "aws_iam_group_policy_attachment" "group_policies" {
  for_each = var.iam_config.group_policy_attachments
  group = aws_iam_group.groups[each.value.group].name
  policy_arn = each.value.policy_arn
}
