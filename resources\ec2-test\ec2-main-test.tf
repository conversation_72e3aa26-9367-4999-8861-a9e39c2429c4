locals {
  env = merge(
    yamldecode(file("${path.module}/../../environments/region.yaml")),
    yamldecode(file("${path.module}/../../environments/webforx.yaml")),
    )
  }

terraform {
  required_version = ">= 1.10.5"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

}

# provider "aws" {
#   region = local.env.dev
# }

# # Configure AWS Provider
# provider "aws" {
#   region = local.env.ec2_config.region
# }


# Security Group Module
module "security_group" {
  source = "/../../modules/sg-module"
  }

# EC2 Instance Module
module "ec2_instance" {
  source = "/../../modules/ec2-module"
  config = local.env.ec2_config

  # Security group outputs from sg-module
  security_group_id   = module.security_group.security_group_id
  security_group_name = module.security_group.security_group_name
  
  depends_on = [module.security_group]
}

# Outputs
output "security_group_info" {
  description = "Security group information"
  value = {
    id   = module.security_group.security_group_id
    name = module.security_group.security_group_name
  }
}

output "ec2_instance_info" {
  description = "EC2 instance information"
  value = {
    instance_id       = module.ec2_instance.instance_id
    public_ip         = module.ec2_instance.instance_public_ip
    private_ip        = module.ec2_instance.instance_private_ip
    public_dns        = module.ec2_instance.instance_public_dns
    availability_zone = module.ec2_instance.availability_zone
    instance_state    = module.ec2_instance.instance_state
  }
}

output "connection_info" {
  description = "Connection information for the EC2 instance"
  value = {
    ssh_command = "ssh -i ~/.ssh/${local.config.ec2_config.key_name}.pem ubuntu@${module.ec2_instance.instance_public_ip}"
    web_url     = "http://${module.ec2_instance.instance_public_ip}"
  }
}

