
Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  [32m+[0m create[0m

Terraform will perform the following actions:

[1m  # module.aws-budget.aws_budgets_budget.monthly_budget[0m will be created
[0m  [32m+[0m[0m resource "aws_budgets_budget" "monthly_budget" {
      [32m+[0m[0m account_id        = (known after apply)
      [32m+[0m[0m arn               = (known after apply)
      [32m+[0m[0m budget_type       = "COST"
      [32m+[0m[0m id                = (known after apply)
      [32m+[0m[0m limit_amount      = "200"
      [32m+[0m[0m limit_unit        = "USD"
      [32m+[0m[0m name              = "development-webforx-monthly-budget"
      [32m+[0m[0m name_prefix       = (known after apply)
      [32m+[0m[0m tags_all          = (known after apply)
      [32m+[0m[0m time_period_end   = "2087-06-15_00:00"
      [32m+[0m[0m time_period_start = (known after apply)
      [32m+[0m[0m time_unit         = "MONTHLY"

      [32m+[0m[0m cost_filter (known after apply)

      [32m+[0m[0m cost_types (known after apply)

      [32m+[0m[0m notification {
          [32m+[0m[0m comparison_operator        = "GREATER_THAN"
          [32m+[0m[0m notification_type          = "ACTUAL"
          [32m+[0m[0m subscriber_email_addresses = [
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
            ]
          [32m+[0m[0m subscriber_sns_topic_arns  = []
          [32m+[0m[0m threshold                  = 100
          [32m+[0m[0m threshold_type             = "PERCENTAGE"
        }
      [32m+[0m[0m notification {
          [32m+[0m[0m comparison_operator        = "GREATER_THAN"
          [32m+[0m[0m notification_type          = "ACTUAL"
          [32m+[0m[0m subscriber_email_addresses = [
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
              [32m+[0m[0m "<EMAIL>",
            ]
          [32m+[0m[0m subscriber_sns_topic_arns  = []
          [32m+[0m[0m threshold                  = 80
          [32m+[0m[0m threshold_type             = "PERCENTAGE"
        }
    }

[1m  # module.aws-budget.aws_sns_topic.budget_alerts[0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic" "budget_alerts" {
      [32m+[0m[0m arn                         = (known after apply)
      [32m+[0m[0m beginning_archive_time      = (known after apply)
      [32m+[0m[0m content_based_deduplication = false
      [32m+[0m[0m fifo_topic                  = false
      [32m+[0m[0m id                          = (known after apply)
      [32m+[0m[0m name                        = "development-webforx-aws-budget-alerts"
      [32m+[0m[0m name_prefix                 = (known after apply)
      [32m+[0m[0m owner                       = (known after apply)
      [32m+[0m[0m policy                      = (known after apply)
      [32m+[0m[0m signature_version           = (known after apply)
      [32m+[0m[0m tags_all                    = (known after apply)
      [32m+[0m[0m tracing_config              = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1m  # module.aws-budget.aws_sns_topic_subscription.email_subscriptions["<EMAIL>"][0m will be created
[0m  [32m+[0m[0m resource "aws_sns_topic_subscription" "email_subscriptions" {
      [32m+[0m[0m arn                             = (known after apply)
      [32m+[0m[0m confirmation_timeout_in_minutes = 1
      [32m+[0m[0m confirmation_was_authenticated  = (known after apply)
      [32m+[0m[0m endpoint                        = "<EMAIL>"
      [32m+[0m[0m endpoint_auto_confirms          = false
      [32m+[0m[0m filter_policy_scope             = (known after apply)
      [32m+[0m[0m id                              = (known after apply)
      [32m+[0m[0m owner_id                        = (known after apply)
      [32m+[0m[0m pending_confirmation            = (known after apply)
      [32m+[0m[0m protocol                        = "email"
      [32m+[0m[0m raw_message_delivery            = false
      [32m+[0m[0m topic_arn                       = (known after apply)
    }

[1mPlan:[0m 8 to add, 0 to change, 0 to destroy.
[0m[90m
─────────────────────────────────────────────────────────────────────────────[0m

Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
